#!/usr/bin/env python3
"""
重启检测脚本测试
验证check_restarts.py的各项功能
"""

import subprocess
import sys
import os

def run_command(cmd, expect_success=True):
    """运行命令并检查结果"""
    print(f"🔍 测试命令: {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        
        if expect_success and result.returncode != 0:
            print(f"❌ 命令失败 (返回码: {result.returncode})")
            print(f"错误输出: {result.stderr}")
            return False
        elif not expect_success and result.returncode == 0:
            print(f"❌ 命令应该失败但成功了")
            return False
        else:
            print(f"✅ 命令执行符合预期")
            if result.stdout:
                # 只显示关键信息
                lines = result.stdout.split('\n')
                for line in lines:
                    if ('总结:' in line or '检测完成' in line or '错误：' in line or 
                        '网关重启检测完成' in line or '设备名称' in line and '真实重启' in line or
                        '检测到' in line and '插件uptime数据异常' in line):
                        print(f"   {line}")
            return True
            
    except subprocess.TimeoutExpired:
        print(f"❌ 命令超时")
        return False
    except Exception as e:
        print(f"❌ 命令执行异常: {e}")
        return False

def main():
    print("🚀 重启检测脚本功能测试")
    print("=" * 60)
    
    # 切换到脚本目录
    script_dir = "/Users/<USER>/maxnetDev/ctgw_restart"
    os.chdir(script_dir)
    
    # 激活虚拟环境的命令前缀
    venv_prefix = "source venv/bin/activate && "
    
    test_cases = [
        # 1. 帮助信息测试
        {
            "name": "帮助信息显示",
            "cmd": f"{venv_prefix}python check_restarts.py --help",
            "expect_success": True
        },
        
        # 2. 正常功能测试 - 短时间范围
        {
            "name": "正常检测 - 1小时范围",
            "cmd": f"{venv_prefix}python check_restarts.py -p 'zhongxing.*' -t 1h",
            "expect_success": True
        },
        
        # 3. 正常功能测试 - 长时间范围
        {
            "name": "正常检测 - 24小时范围",
            "cmd": f"{venv_prefix}python check_restarts.py -p 'zhongxing.*' -t 24h",
            "expect_success": True
        },
        
        # 4. 无匹配设备测试
        {
            "name": "无匹配设备处理",
            "cmd": f"{venv_prefix}python check_restarts.py -p 'nonexistent.*' -t 1h",
            "expect_success": True
        },
        
        # 5. 参数验证测试 - 未来时间
        {
            "name": "参数验证 - 未来时间",
            "cmd": f"{venv_prefix}python check_restarts.py -p 'zhongxing.*' -t '2025-12-31 10:00'",
            "expect_success": False
        },
        
        # 6. 参数验证测试 - 无效正则表达式
        {
            "name": "参数验证 - 无效正则",
            "cmd": f"{venv_prefix}python check_restarts.py -p '[invalid' -t 1h",
            "expect_success": False
        },
        
        # 7. 参数验证测试 - 缺少必需参数
        {
            "name": "参数验证 - 缺少pattern",
            "cmd": f"{venv_prefix}python check_restarts.py -t 1h",
            "expect_success": False
        },
        
        # 8. 自定义参数测试
        {
            "name": "自定义异常容忍时间",
            "cmd": f"{venv_prefix}python check_restarts.py -p 'zhongxing.*' -t 1h --anomaly-tolerance 10m",
            "expect_success": True
        },
        
        # 9. 网关参数测试 - 自定义网关URL
        {
            "name": "网关参数 - 自定义URL",
            "cmd": f"{venv_prefix}python check_restarts.py -p 'zhongxing.*' -t 1h --gateway-url http://192.168.0.27:21000",
            "expect_success": True
        },
        
        # 10. 网关参数测试 - 自定义重启阈值
        {
            "name": "网关参数 - 自定义重启阈值",
            "cmd": f"{venv_prefix}python check_restarts.py -p 'zhongxing.*' -t 1h --gateway-threshold 15m",
            "expect_success": True
        },
        
        # 11. 网关参数验证 - 无效URL
        {
            "name": "网关参数验证 - 无效URL",
            "cmd": f"{venv_prefix}python check_restarts.py -p 'zhongxing.*' -t 1h --gateway-url invalid-url",
            "expect_success": False
        },
        
        # 12. 网关参数验证 - 无效阈值格式
        {
            "name": "网关参数验证 - 无效阈值",
            "cmd": f"{venv_prefix}python check_restarts.py -p 'zhongxing.*' -t 1h --gateway-threshold invalid-time",
            "expect_success": False
        },
        
        # 13. 完整的网关功能测试
        {
            "name": "完整网关功能测试",
            "cmd": f"{venv_prefix}python check_restarts.py -p 'zhongxing.*' -t 1h --gateway-url http://192.168.0.27:21000 --gateway-threshold 30m --anomaly-tolerance 5m",
            "expect_success": True
        },
        
        # 14. 新输出格式验证测试
        {
            "name": "新输出格式验证",
            "cmd": f"{venv_prefix}python check_restarts.py -p 'zhongxing.*' -t 1h | grep -E '(设备名称.*真实重启.*数据异常.*最后重启时间\\(网关重启\\)|总结:.*插件发生真实重启|检测到.*次插件uptime数据异常)'",
            "expect_success": True
        }
    ]
    
    passed = 0
    total = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}/{total}: {test_case['name']}")
        print("-" * 40)
        
        success = run_command(test_case['cmd'], test_case['expect_success'])
        if success:
            passed += 1
        
        print()
    
    # 测试总结
    print("=" * 60)
    print(f"📊 测试总结: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！脚本功能正常")
        return 0
    else:
        print(f"❌ {total - passed} 个测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
