-- 家庭流媒体高流量测试脚本
-- 模拟8台不同设备的网络行为

local counter = 0
local devices = {
    "Mozilla/5.0 (Smart TV; Tizen 4.0)",
    "Mozilla/5.0 (iPad; CPU OS 15_0)",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0)",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/91.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) Safari/605.1",
    "Mozilla/5.0 (Android 11; Mobile) Chrome/91.0",
    "Mozilla/5.0 (Linux; Android 11; TV) Chrome/91.0",
    "Mozilla/5.0 (X11; Linux x86_64) Firefox/89.0"
}

local video_qualities = {"4K", "1080p", "720p", "480p"}
local content_types = {
    "/api/video/stream/",
    "/api/live/channel/",
    "/api/movie/play/",
    "/api/series/episode/"
}

function setup(thread)
    thread:set("id", counter)
    counter = counter + 1
end

function init(args)
    math.randomseed(os.time())
end

function request()
    -- 随机选择设备类型
    local device = devices[math.random(1, #devices)]
    local quality = video_qualities[math.random(1, #video_qualities)]
    local content = content_types[math.random(1, #content_types)]
    local video_id = math.random(1000, 9999)
    
    -- 构建请求路径
    local path = content .. video_id .. "?quality=" .. quality
    
    -- 设置请求头，模拟真实流媒体请求
    local headers = {}
    headers["User-Agent"] = device
    headers["Accept"] = "video/mp4,video/webm,application/vnd.apple.mpegurl,*/*"
    headers["Accept-Encoding"] = "gzip, deflate, br"
    headers["Connection"] = "keep-alive"
    headers["Range"] = "bytes=0-1048576"  -- 请求1MB数据块
    
    -- 模拟不同类型的请求
    local request_type = math.random(1, 10)
    if request_type <= 7 then
        -- 70% 视频流请求
        headers["Content-Type"] = "video/mp4"
        return wrk.format("GET", path, headers)
    elseif request_type <= 9 then
        -- 20% 缓冲区预加载请求
        headers["X-Preload"] = "true"
        return wrk.format("GET", path .. "&preload=true", headers)
    else
        -- 10% 元数据请求
        return wrk.format("GET", "/api/metadata/" .. video_id, headers)
    end
end

function response(status, headers, body)
    if status ~= 200 and status ~= 206 then
        print("Error response: " .. status)
    end
end

function done(summary, latency, requests)
    print("=====================================================")
    print("家庭流媒体高流量测试完成")
    print("模拟设备数量: 8台")
    print("总请求数: " .. summary.requests)
    print("平均延迟: " .. latency.mean/1000 .. "ms")
    print("错误数: " .. summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout)
    print("吞吐量: " .. summary.requests/summary.duration * 1000000 .. " req/sec")
    print("数据传输: " .. summary.bytes/(1024*1024) .. " MB")
    print("=====================================================")
end
