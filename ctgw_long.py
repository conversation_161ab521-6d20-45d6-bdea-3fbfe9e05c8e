#!/usr/bin/env python3

import re,sys, os, time, json, csv
import shlex, subprocess
from prometheus_client import start_http_server, Gauge, Summary
import jsonpath, base64

g = Gauge('dev_mem_ustest', 'memory usage for each test device', ['sn','name', 'type'])

def cmd_execute(sn, url, subtype, action, argcmd = None, keycmd = "args"):
    cmdprefix = "curl  -s %s -X POST -H 'Content-type: application/json' -d '" %url
    if argcmd:
        cmd = cmdprefix + '{"sn":"%s","cmd":{"subtype":"%s","action":"%s","%s":"%s"}}' %(sn, subtype, action, keycmd, argcmd) + "'"
    else:
        cmd = cmdprefix + '{"sn":"%s","cmd":{"subtype":"%s","action":"%s"}}' %(sn, subtype, action) + "'"
    args = shlex.split(cmd)
    info = subprocess.check_output(args)
    #print("debug",info)
    return info

def calc_cpuinfo(old_all,old_idle, cpu_all,cpu_idle):
    total = float(cpu_all) - float(old_all)
    idle =  float(cpu_idle) - float(old_idle)
    if total > 0.0 and idle > 0.0 and total > idle:
       return 100*(total-idle)/total
    return  0

def calc_innerinfo(old_cpuall,old_innerall, cpuall,innerall):
    total = float(cpuall) - float(old_cpuall)
    inner =  float(innerall) - float(old_innerall)
    if total > 0.0 and inner > 0.0 and total >inner:
        return 100*inner/total
    return  0


def cpu_idle_all(new):
    i = 1
    cpu_all = 0
    cpu_idle = 0
    new_list = new.replace('\\n','').replace('"','')
    aa = new_list.split()
    if len(aa)>3:
       try:
          while  i < len(aa):
             cpu_all += float(aa[i])
             i += 1
          cpu_idle = aa[4]
       except:
             cpu_idle = 0
             cpu_all = 0
    return cpu_all,cpu_idle

def inner_cpu_all(new):
    i = 13
    inner_total = 0
    new_list = new.replace('\\n','').replace('"','')
    aa = new_list.split()
    if len(aa)>3:
        try:
          while  i <= 16:
             inner_total += float(aa[i])
             i = i+1
        except:
             inner_total = 0
    return inner_total

def deal_meminfo(d):
    l = {}
    for i in d.replace("\"","").split('\\n'):
        kv = i.split(':')
        if len(kv) != 2:
            continue
        k = kv[0].strip()
        v = kv[1].strip().split(' ')
        if len(v) == 0:
            continue
        l[k]=int(v[0])
    return l

def get_mem(sn,url = "************:21000/api/v1/diagnose/do"):
    _meminfo = cmd_execute(sn, url, "shell", "read", "cat /proc/meminfo")
    meminfo = deal_meminfo(str(_meminfo, encoding = "utf-8"))
    if len(meminfo) < 4:
        print(_meminfo)
        return 0
    print(meminfo)
    if 'MemAvailable' in meminfo:
        sys_use = meminfo['MemTotal'] - meminfo['MemAvailable']
    else:
        sys_use = meminfo['MemTotal'] - meminfo['MemFree'] - meminfo['Buffers'] - meminfo['Cached']
    print(sys_use)
    return sys_use


def systime(sn,url = "************:21000/api/v1/diagnose/do"):
    systime = cmd_execute(sn, url, "shell", "read", "cat /proc/uptime")
    systime = str(systime, encoding = "utf-8")
    sys_time = 0
    try:
       if ("error" not in systime) and len(systime.split()) >1 and len(systime.split()) < 3 :
          sys_time1 = systime.split()[0]
          sys_time = sys_time1[1:]
    except:
          sys_time = 0
    return sys_time


def get_uptime(sn, url):
    uptime = cmd_execute(sn, url, "sys", "dpi_uptime")
    uptime = str(uptime, encoding = "utf-8")
    ut_val = 0
    try:
       if "error" not in uptime:
          ut_val = uptime
    except:
       ut_val = 0
    return ut_val

#get_memory
def get_memory(sn,url):
    memproc = cmd_execute(sn, url, "sys", "load",
                          "f=require[[io]].open([[/proc/self/status]]);a=f:read([[*a]]);f:close();return a", "string")
    memproc = str(memproc, encoding = "utf-8")
    proc_used_list = memproc.split("\\n")
    proc_used = 0
    for i in proc_used_list:
        if len(i) > 5 and i[:5] == "VmRSS":
            proc_used = i.split()[1]
            break
    return proc_used

# lua memory
def get_lua_mem(sn,url):
    memlua = cmd_execute(sn, url, "sys", "show_memory")
    lua_used = 0
    try :
       lm = json.loads(memlua)
       if 'used' in lm:
           lua_used = lm['used'].split()[0]
    except:
           lua_used = 0
    return lua_used

# get __dpi_info  get deal_pkt_count deal_ctx_count uoload tlv succ_stat fail_stat
def  get_dpi_info(sn,url = "************:21000/api/v1/diagnose/do"):
     dpi_info = cmd_execute(sn, url, "__dpi_info", "read")
     dpi_info = str(dpi_info,encoding = "utf-8")
     global deal_pkt_count,deal_ctx_count,tlv_succcont,tlv_failcont,json_succcont,json_failcont
     if 'dpi_stat' not in dpi_info:
         pass

     try:
         deal_pkt_count =  jsonpath.jsonpath(json.loads(dpi_info), "$..dpi_stat.deal_pkt_count")[0]
         deal_ctx_count =  jsonpath.jsonpath(json.loads(dpi_info), "$..dpi_stat.deal_ctx_count")[0]
         tlv_succcont   =  jsonpath.jsonpath(json.loads(dpi_info),"$..svr_data.upload_succ_count")[0]
         tlv_failcont   =  jsonpath.jsonpath(json.loads(dpi_info),"$..svr_data.upload_fail_count")[0]
         json_succcont  =  jsonpath.jsonpath(json.loads(dpi_info),"$..svr_data.datajson_peer.upload_succ_count")[0]
         json_failcont  =  jsonpath.jsonpath(json.loads(dpi_info),"$..svr_data.datajson_peer.upload_fail_count")[0]
     except:
         print("get_dpi_info error")
         return 0,0,0,0,0,0

     return deal_pkt_count,deal_ctx_count,tlv_succcont,tlv_failcont,json_succcont,json_failcont

def sn_result(sn, url, old_sn):
    #url = "************:21000/api/v1/diagnose/do"
    saved_sn = {"cpuall":0, "cpuidle":0, "delete":False, "gauge":[]}
    # get mem_info/process mem/lua mem
    sys_free = get_mem(sn,url = "************:21000/api/v1/diagnose/do")
    saved_sn['gauge'].append({"sn":sn, "type":"sys", "v":sys_free})

    # get systime
    sys_time = systime(sn,url = "************:21000/api/v1/diagnose/do")
    saved_sn['gauge'].append({"sn":sn, "type":"systime", "v":sys_time})

    # get dpi_info deal_pkt_count
    deal_pkt_count, deal_ctx_count, tlv_succcont,tlv_failcont, json_succcont ,json_failcont= get_dpi_info(sn,url = "************:21000/api/v1/diagnose/do")
    saved_sn['gauge'].append({"sn":sn, "type":"deal_pkt_count", "v":deal_pkt_count})

    # get dpi_info deal_ctx_count 
    saved_sn['gauge'].append({"sn":sn, "type":"deal_ctx_count", "v":deal_ctx_count})
    # get dpi_info svrdatt tlv
    saved_sn['gauge'].append({"sn":sn, "type":"tlv_succcont", "v":tlv_succcont})    
    saved_sn['gauge'].append({"sn":sn, "type":"tlv_failcont", "v":tlv_failcont})      
    # get dpi_info svrdatt json
    saved_sn['gauge'].append({"sn":sn, "type":"json_succcont", "v":json_succcont})
    saved_sn['gauge'].append({"sn":sn, "type":"json_failcont", "v":json_failcont})

    # get cpu
    now_cpuinfo = cmd_execute(sn, url, "shell", "read", "head -1 /proc/stat")
    now_cpuinfo = str(now_cpuinfo, encoding = "utf-8")
    cpu_pct = 0
    cpu_all = 0
    cpu_idle = 0
    try:
        if ("error" not in now_cpuinfo):
           cpu_all,cpu_idle = cpu_idle_all(now_cpuinfo)
           if  (sn in old_sn) and old_sn[sn]['cpuall'] != 0 and  old_sn[sn]['cpuidle'] != 0 :
               cpu_pct = calc_cpuinfo(old_sn[sn]['cpuall'], old_sn[sn]['cpuidle'],cpu_all,cpu_idle)
           else:
               cpu_pct = 0
           #    old_sn[sn]['delete'] = True
    except:
       cpu_pct = 0
    saved_sn['cpuall'] = cpu_all
    saved_sn['cpuidle'] = cpu_idle
    saved_sn['gauge'].append({"sn":sn, "type":"syscpu", "v":cpu_pct})

    # get_inner_cpu
    saved_sn = {"cpu_all":0, "cpu_inner":0, "delete":False, "gauge":[]}
    cpu_info = cmd_execute(sn, url, "shell", "read", "head -1 /proc/stat")
    inner_info = cmd_execute(sn, url, "shell", "read", "head -1 /proc/`cat /tmp/.uci/app_b01_run.pid`/stat")
    now_cpu_info = str(cpu_info,encoding = "utf-8")
    now_inner_info = str (inner_info,encoding = "utf-8")
    inner_total = 0
    allcpu = 0
    inner_cpt = 0
    try:
        if ("error" not in now_inner_info):
            inner_total = inner_cpu_all(now_inner_info)
            allcpu = cpu_idle_all(now_cpu_info)[1]
            if  (sn in old_sn) and old_sn[sn]['cpu_inner'] != 0 and  old_sn[sn]['cpu_all'] != 0 :
                    inner_cpt = calc_innerinfo(old_sn[sn]['cpu_all'], old_sn[sn]['cpu_inner'], allcpu, inner_total)
            else:
                inner_cpt = 0
    except:
       inner_cpt =0
    saved_sn['cpu_all'] = allcpu
    saved_sn['cpu_inner'] = inner_total
    saved_sn['gauge'].append({"sn":sn, "type":"innercpu", "v":inner_cpt})

    #get uptime
    ut_val = get_uptime(sn, url)
    saved_sn['gauge'].append({"sn":sn, "type":"uptime", "v":ut_val})

    # process memory
    proc_used = get_memory(sn,url)
    saved_sn['gauge'].append({"sn":sn, "type":"inner", "v":proc_used})

    # lua memory
    lua_used = get_lua_mem(sn,url)
    saved_sn['gauge'].append({"sn":sn, "type":"lua", "v":lua_used})

    # m01 processes num
    #process = cmd_execute(sn, url, "shell", "read", "netstat -natpl |grep m01-b01-inner |wc -l")
    example = cmd_execute(sn, url, "shell", "read", "ls /tmp/.uci/app_b01_run.pid")
    if len(example) == 32:
       process = cmd_execute(sn, url, "shell", "read", "ls -l /proc/`cat /tmp/.uci/app_b01_run.pid`/fd/ | grep socket| wc -l")
    else:
      process = cmd_execute(sn, url, "shell", "read", "ls -l /proc/`cat /CuInform/tmp/.uci/app_b01_run.pid`/fd/ | grep socket| wc -l")
    
    process = str(process, encoding = "utf-8")
    sys_proc = 0
    cmd1= re.findall(r"\d+",process)
    if len(cmd1) > 0 :
        sys_proc = cmd1[0]
#    saved_sn['gauge'].append({"sn":sn, "type":"process", "v":sys_proc})
#    return saved_sn
        sys_proc = int(sys_proc)
        if sys_proc > 7:
           mylog = open('socket.log','a+')
           con = cmd_execute(sn, url, "shell", "read", "netstat -natpl")
           con = str(con, encoding = "utf-8")
           for line in con.split("\\n"):
               # print(line,file=mylog)
               mylog.writelines("%%%%%%%%%%%%%%%"+sn+"&&&&&&&&&&&&&&&&&&&"+'\n')
               mylog.writelines(line)
               mylog.write('\n')
           mylog.close()
        #else:
         # print(type(sys_proc))


    saved_sn['gauge'].append({"sn":sn, "type":"process", "v":sys_proc})
    return saved_sn

def process_request(period, host, snlist, saved_sn):
    # get list
    cmd = "curl  -s %s/api/v1/diagnose/list" %host
    yy = subprocess.check_output(shlex.split(cmd))
    snall = json.loads(yy)
    #print(snall)
    
    url = host + "/api/v1/diagnose/do"
#    snall = ["FCF647CDE7EA"]

    return_sn = {}
    #for sn in snall:
    for sn in snlist:
        return_sn[sn] = sn_result(sn, url, saved_sn)
        print("snresult:", sn, return_sn[sn])

    #print(len(snall),len(return_sn))
    for i in return_sn:
        name = ""
        if i in snlist:
            name = snlist[i]
        svalue = return_sn[i]
        svalue['name'] = name
        for j in svalue['gauge']:
            try:
                g.labels(sn = j["sn"], name = name, type = j["type"]).set(j["v"])
            except:
                continue

    # delete old labels
    for i in saved_sn:
        if  i not  in snall :
       # if saved_sn[i]['delete']:
           for j in saved_sn[i]['gauge']:
               try:
                  g.remove(j["sn"],saved_sn[i]["name"], j["type"])
               except:
                  print(j)
    #print(return_sn)
    time.sleep(period)
    return return_sn

def sncsv_load(fname):
    snlist = {}
    try:
        with open(fname,encoding='utf-8') as f:
            reader = csv.reader(f)
            next(reader)            # ignore first line
            for row in reader:
                if len(row) != 2:
                    continue
                snlist[row[0]] = row[1]
    except IOError:
        print ("Error: failed to read")
    return snlist

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('-c', '--csv', type = str, default = "t.csv", help = "sn/name csv files")
    parser.add_argument('-p', '--port', type = int, default = 9003, help = "metric http port")
    parser.add_argument('-a', '--longaddr', type = str, default = "************:21000", help = "where longconn is running")
    parser.add_argument('-t', '--period', type = int, default = 6, help = "check period")
    args = parser.parse_args()

    start_http_server(args.port)
    snlist = sncsv_load(args.csv)
    saved_sn = {}
    while True:
        saved_sn = process_request(args.period, args.longaddr, snlist, saved_sn)
