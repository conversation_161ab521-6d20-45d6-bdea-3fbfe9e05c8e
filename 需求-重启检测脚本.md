## 📋 重启检测脚本 - 简化需求文档

### 🎯 目标
创建一个**简单、专注**的工具，快速检测设备重启情况

### 🏗️ 设计原则
- **简单**: 最少参数，最直观输出
- **专注**: 只做重启检测，不做其他  
- **快速**: 10秒内得到结果

### 📊 核心功能
1. 从Prometheus获取设备uptime数据
2. 从Prometheus获取设备进程实际物理内存占用数据
3. 检测uptime下降事件（任何下降都可能是重启）
4. 区分真实重启和数据异常：
   - **数据异常**：uptime短暂降为0后快速恢复到异常前水平或更高
   - **真实重启**：uptime降为0后逐步正常增长
5. 统计每台设备在检测时间段内每次重启前的实际物理内存占用
6. 计算并显示每台设备重启前内存占用的最小值、最大值和平均值
7. **新增**：统计每台设备在检测时间段内每次重启后的实际物理内存占用
8. **新增**：支持配置重启后内存检测范围（如5m、10m、20m、30m等），在该范围内取内存的最大值
9. **新增**：计算并显示每台设备重启后内存占用的最小值、最大值和平均值
10. **网关重启检测**：对于插件发生真实重启的设备，检测网关是否同时重启：
   - 从接口(192.168.0.27:21000)实时获取网关本身的sys_uptime和插件本身的plg_uptime
   - 比较这两个实时数据：plg_uptime与sys_uptime的差值
   - 如果时间相差不超过30分钟，认为网关发生过重启
11. 统计并显示确认的真实重启结果、重启前后内存统计和网关重启情况
12. **新增**：支持将统计结果表格导出为CSV文件，便于后续数据分析和存档

### 💻 用户接口

**脚本名称**: `check_restarts.py`

**基本用法**:
```bash
# 检查所有设备过去24小时
python3 check_restarts.py -p ".*"

# 检查特定设备过去1小时  
python3 check_restarts.py -p "zhongxing.*" -t 1h

# 检查从指定时间到现在的重启情况
python3 check_restarts.py -p "device.*" -t "2025-01-30 10:00"

# 调整数据异常容忍窗口
python3 check_restarts.py -p "device.*" --anomaly-tolerance 10m

# 配置重启后内存检测范围为20分钟
python3 check_restarts.py -p "device.*" --post-restart-range 20m

# 导出结果为CSV文件
python3 check_restarts.py -p "zhongxing.*" -t 24h --csv-output restart_report.csv
```

**参数设计**（7个参数）:
| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `-p, --pattern` | 设备名称正则（**必需**） | 无 | `"zhongxing.*"` |
| `-t, --time` | 时间范围 | `24h` | `1h`, `6h`, `24h`, `7d`, `"2025-01-30 10:00"` |
| `--anomaly-tolerance` | 数据异常容忍窗口 | `5m` | `3m`, `10m` |
| `--post-restart-range` | 重启后内存检测范围 | `5m` | `5m`, `10m`, `20m`, `30m` |
| `--url` | Prometheus地址 | `http://192.168.0.25:9090` | 自定义URL |
| `--gateway-url` | 网关接口地址 | `http://192.168.0.27:21000` | 网关sys_uptime接口 |
| `--csv-output` | CSV导出文件路径（可选） | 无 | `restart_report.csv`, `./reports/data.csv` |


### 📤 输出格式（统一简单）
```
设备名称                SN            真实重启  数据异常   最后重启时间(网关重启)           重启前内存(最小/最大/平均)  重启后内存(最小/最大/平均)
device001              389E80BB4EE9  2        1        2025-01-31 08:30:15(是)  32.5MB/45.8MB/39.2MB   28.1MB/35.2MB/31.7MB
device002              F2A1B3C4D5E6  0        0        -                    -                       -
device003              A7B8C9D0E1F2  1        2        2025-01-30 15:45:30(否)  28.3MB/28.3MB/28.3MB   25.8MB/25.8MB/25.8MB

────────────────────────────────────────────────────────────────
时间范围: 2025-01-30 10:00:00 - 2025-01-31 10:00:00
检查设备: 5台 (异常容忍: 5分钟, 重启后内存检测范围: 5分钟)

总结: 2/5台设备插件发生真实重启 (40%)，其中1/2台设备网关发生过重启(50%)
     检测到3次插件uptime数据异常 (已过滤)
```

#### 📝 输出格式调整与检测详情字段说明

- **输出顺序调整**：将"时间范围"、"设备数量"、"异常容忍"等汇总信息放在**输出末尾**，便于用户一眼看到检测结果后再查阅统计信息。
- **检测详情表格**：在原有表格基础上，**增加一列 `SN`**（设备序列号或唯一标识），便于区分同名设备或后续追溯。
- **网关重启标识列**：修改"最后重启时间"列为"最后重启时间(网关重启)"：
  - **格式**：`2025-01-31 08:30:15(是)` 或 `2025-01-30 15:45:30(否)`
  - **网关重启判断**：仅对插件发生真实重启的设备进行检测
     - **判断标准**：网关sys_uptime与插件plg_uptime相差不超过30分钟为"是"，否则为"否"
  - **无重启设备**：显示"-"表示无重启记录
- **内存统计列**：新增"重启前内存(最小/最大/平均)"列，显示每台设备在检测时间段内所有重启前的实际物理内存占用统计：
  - **最小值**：所有重启前实际物理内存占用的最小值
  - **最大值**：所有重启前实际物理内存占用的最大值
  - **平均值**：所有重启前实际物理内存占用的平均值
  - **格式**：内存单位显示，自动选择合适单位(KB/MB)，保留1位小数，如"32.5MB/45.8MB/39.2MB"
  - **无重启设备**：显示"-"表示无重启记录
- **重启后内存统计列**：新增"重启后内存(最小/最大/平均)"列，显示每台设备在检测时间段内所有重启后的实际物理内存占用统计：
  - **检测范围**：通过`--post-restart-range`参数配置，默认为5分钟，支持5m、10m、20m、30m等
  - **数据采集**：在重启后的指定时间范围内采集内存数据点
  - **最大值优先**：在检测范围内取内存的最大值作为该次重启后的内存占用值
  - **统计计算**：基于所有重启后内存最大值，计算最小值、最大值和平均值
  - **格式**：内存单位显示，自动选择合适单位(KB/MB)，保留1位小数，如"28.1MB/35.2MB/31.7MB"
  - **无重启设备**：显示"-"表示无重启记录

### 📊 CSV导出格式

当指定 `--csv-output` 参数时，将同时生成CSV文件，包含与控制台输出相同的数据：

**CSV文件格式**：
```csv
设备名称,SN,真实重启,数据异常,最后重启时间(网关重启),重启前内存(最小/最大/平均),重启后内存(最小/最大/平均)
device001,389E80BB4EE9,2,1,2025-01-31 08:30:15(是),32.5MB/45.8MB/39.2MB,28.1MB/35.2MB/31.7MB
device002,F2A1B3C4D5E6,0,0,-,-,-
device003,A7B8C9D0E1F2,1,2,2025-01-30 15:45:30(否),28.3MB/28.3MB/28.3MB,25.8MB/25.8MB/25.8MB
```

**CSV导出特性**：
- 使用UTF-8编码，支持中文字符
- 使用逗号分隔符，兼容Excel和其他数据分析工具
- 包含完整的表头和数据行
- 文件路径支持相对路径和绝对路径
- 自动创建目录（如果不存在）
- 导出成功后显示文件路径确认

### 🚫 移除的复杂功能
- ❌ 复杂时间解析（绝对时间、yesterday等）
- ❌ 多种输出格式（JSON/simple选择）
- ❌ 调试模式的详细打印
- ❌ 文件输出功能
- ❌ 详细timeline和数据结构
- ❌ 内置测试功能
- ❌ 可读时间格式转换

### 🎯 技术约束
- **简单高效实现**：可使用第三方库，优先选择简洁高效的解决方案
- **依赖管理**：提供 `requirements.txt` 依赖说明文件
- **单文件实现**
- **代码行数 < 300行**（包含网关重启检测功能）
- **运行时间 < 10秒**

### 📦 推荐技术栈
- **HTTP请求**: `requests` (比urllib更简洁)
- **时间处理**: `pendulum` 或 `arrow` (比datetime更直观)
- **参数解析**: `argparse` (标准库，功能够用)
- **JSON处理**: `json` (标准库，无需替换)

### 🔧 实现重点
1. **简化时间处理**: 支持相对时间和具体时间两种模式
   - **相对时间**: `1h`, `6h`, `24h`, `7d` (从现在向前推算)
   - **具体时间**: `"2025-01-30 10:00"`, `"2025-01-30 10:30:45"` (从指定时间到现在)
   - **时间验证**: 具体时间不能大于等于当前时间
2. **智能重启检测**: 区分真实重启和数据异常
3. **简化输出**: 固定表格格式，显示真实重启和异常统计
4. **简化错误处理**: 基本的try-catch
5. **去除调试**: 遇到问题直接报错退出

### 🧠 检测逻辑设计
**新的重启检测算法**：
1. **发现uptime下降** → 标记为"疑似重启事件"
2. **异常检查窗口**（`anomaly-tolerance`时间内）：
   - 如果uptime恢复到下降前水平或更高 → **数据异常**，记录但不计入重启
   - 如果uptime持续低值并正常增长 → **确认重启**
3. **内存数据收集**：
   - **重启前内存**：对于每个确认的重启事件，获取重启前最后一个有效的实际物理内存占用数据点
   - **重启后内存**：在重启后的指定时间范围内（`--post-restart-range`参数配置）采集内存数据点，取该范围内的最大值
   - 收集该设备在检测时间段内所有重启前和重启后的内存占用值
   - 分别计算重启前和重启后内存的最小值、最大值和平均值，并转换为合适的内存单位(KB/MB)
4. **网关重启检测**（仅对插件真实重启的设备）：
   - 从网关接口(192.168.0.27:21000)实时获取网关当前sys_uptime和插件当前plg_uptime
   - 验证数据合理性：正常情况下plg_uptime ≤ sys_uptime
   - 计算时间差值：sys_uptime - plg_uptime
   - **判断标准**：时间差 ≤ 30分钟 → 网关重启(是)，否则 → 网关重启(否)
   - **异常处理**：如果plg_uptime > sys_uptime，记录为数据异常，网关重启状态显示为"(-)"
5. **最终统计** → 分别显示真实重启次数、数据异常次数、重启前后内存统计和网关重启统计

---

## 💡 重构建议

这个简化版本将**专注于核心价值**：
- ✅ 智能检测真实重启（过滤数据异常）
- ✅ 简单易用（5个参数）
- ✅ 结果清晰（区分重启和异常）
- ✅ 内存分析（重启前后实际物理内存占用统计，支持配置重启后检测范围）
- ✅ 网关重启关联分析（检测网关与插件同时重启）

相比当前400+行的复杂脚本，新版本将是：
- 📦 **更轻量**: ~250行代码（增加内存统计和网关重启检测功能）
- 🧠 **更智能**: 区分真实重启和数据异常
- 🎯 **更准确**: 基于uptime数据特性的检测逻辑
- 📊 **更全面**: 提供重启前后实际物理内存占用分析和网关重启关联分析
- 🚀 **更快速**: 使用高效第三方库，无复杂调试和多格式输出
- 📋 **易部署**: 提供requirements.txt，一键安装依赖